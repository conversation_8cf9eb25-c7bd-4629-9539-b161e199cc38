<ng-container [ngSwitch]="source">
  <ng-container *ngSwitchCase="Google" [ngTemplateOutlet]="gmb"></ng-container>
  <ng-container *ngSwitchCase="Facebook" [ngTemplateOutlet]="facebook"></ng-container>
  <ng-container *ngSwitchCase="Instagram" [ngTemplateOutlet]="instagram"></ng-container>
  <ng-container *ngSwitchCase="X" [ngTemplateOutlet]="twitter"></ng-container>
  <ng-container *ngSwitchCase="Bing" [ngTemplateOutlet]="bing"></ng-container>
</ng-container>

<ng-template #gmb>
  <mat-card appearance="outlined">
    <mat-card-header>
      <mat-card-title>
        {{ 'LISTING_SYNC.LS_FREE_ADDON.GOOGLE_MY_BUSINESS.TOOLTIP.TITLE' | translate }}
      </mat-card-title>
    </mat-card-header>
    <mat-dialog-content>
      {{ 'LISTING_SYNC.LS_FREE_ADDON.GOOGLE_MY_BUSINESS.TOOLTIP.TEXT' | translate }}
      <br />
      <h3>
        {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.LOCATION_HEADER' | translate }}
      </h3>
      <div>
        <ul>
          <li>
            {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.BUSINESS_NAME' | translate }}
          </li>
          <li>
            {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.ADDRESS' | translate }}
          </li>
          <li>
            {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.PRIMARY_PHONE' | translate }}
          </li>
          <li>
            {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.WEBSITE' | translate }}
          </li>
          <li>
            {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.HOURS_AND_SPECIAL_HOURS' | translate }}
          </li>
          <li>
            {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.GOOGLE_ATTRIBUTES' | translate }}
          </li>
          <li>
            {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.LONG_DESCRIPTION' | translate }}
          </li>
           <li>
            {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.SOCIAL_MEDIA_URLS' | translate }}
          </li>
           <li>
            {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.BUSINESS_HOURS' | translate }}
          </li>
        </ul>
        <br />
        <h3>
          {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.MEDIA_HEADER' | translate }}
        </h3>
        <ul>
          <li>
            {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.IMAGE_TIP_FORMAT' | translate }}
          </li>
          <li>
            {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.IMAGE_TIP_SIZE' | translate }}
          </li>
          <li>
            <strong>
              {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.LOGO' | translate }}
            </strong>
          </li>
          <ul>
            <li>
              {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.IMAGE_TIP_RECOMMENDED_RESOLUTION_LOGO' | translate }}
            </li>
          </ul>
          <li>
            <strong>
              {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.COVER_PHOTO' | translate }}
            </strong>
          </li>
          <ul>
            <li>
              {{
                'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.IMAGE_TIP_RECOMMENDED_ASPECT_RATIO_PRIMARY_PHOTO' | translate
              }}
            </li>
            <li>
              {{
                'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.IMAGE_TIP_RECOMMENDED_RESOLUTION_PRIMARY_PHOTO' | translate
              }}
            </li>
            <li>
              {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.IMAGE_TIP_QUALITY' | translate }}
            </li>
          </ul>
        </ul>
      </div>
    </mat-dialog-content>
  </mat-card>
</ng-template>

<ng-template #facebook>
  <mat-card appearance="outlined">
    <mat-card-header>
      <mat-card-title>
        {{ 'LISTING_SYNC.LS_FREE_ADDON.FACEBOOK.TOOLTIP.TITLE' | translate }}
      </mat-card-title>
    </mat-card-header>
    <hr />
    <mat-dialog-content>
      {{ 'LISTING_SYNC.LS_FREE_ADDON.FACEBOOK.TOOLTIP.TEXT' | translate }}
      <div>
        <ul>
          <li>
            {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.ADDRESS' | translate }}
          </li>
          <li>
            {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.PRIMARY_PHONE' | translate }}
          </li>
          <li>
            {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.WEBSITE' | translate }}
          </li>
          <li>
            {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.HOURS' | translate }}
          </li>
          <li>
            {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.PAYMENT' | translate }}
          </li>
          <li>
            <span [innerHTML]="'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.LOGO' | translate"></span>
          </li>
          <li>
            {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.LONG_DESCRIPTION' | translate }}
          </li>
        </ul>
      </div>
    </mat-dialog-content>
  </mat-card>
</ng-template>

<ng-template #instagram>
  <mat-card appearance="outlined">
    <mat-card-header>
      <mat-card-title>
        {{ 'LISTING_SYNC.LS_FREE_ADDON.INSTAGRAM.TOOLTIP.TITLE' | translate }}
      </mat-card-title>
    </mat-card-header>
    <hr />
    <mat-dialog-content>
      {{ 'LISTING_SYNC.LS_FREE_ADDON.INSTAGRAM.TOOLTIP.TEXT' | translate }}
      <div>
        <ul>
          <li>
            {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.ADDRESS' | translate }}
          </li>
        </ul>
      </div>
    </mat-dialog-content>
  </mat-card>
</ng-template>

<ng-template #twitter>
  <mat-card appearance="outlined">
    <mat-card-header>
      <mat-card-title>
        {{ 'LISTING_SYNC.LS_FREE_ADDON.TWITTER.TOOLTIP.TITLE' | translate }}
      </mat-card-title>
    </mat-card-header>
    <hr />
    <mat-dialog-content>
      {{ 'LISTING_SYNC.LS_FREE_ADDON.TWITTER.TOOLTIP.TEXT' | translate }}
      <div>
        <ul>
          <li>
            {{ 'LISTING_SYNC.LS_FREE_ADDON.TWITTER.TOOLTIP.ADDRESS' | translate }}
          </li>
          <li>
            {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.WEBSITE' | translate }}
          </li>
          <li>
            <span [innerHTML]="'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.LOGO' | translate"></span>
          </li>
          <li>
            {{ 'LISTING_SYNC.LS_FREE_ADDON.TWITTER.TOOLTIP.SHORT_DESC' | translate }}
          </li>
        </ul>
      </div>
    </mat-dialog-content>
  </mat-card>
</ng-template>

<ng-template #bing>
  <mat-card appearance="outlined">
    <mat-card-header>
      <mat-card-title>
        {{ 'LISTING_SYNC.PRIMARY_SOURCES.BING.TOOLTIP.TITLE' | translate }}
      </mat-card-title>
    </mat-card-header>
    <hr />
    <mat-dialog-content>
      {{ 'LISTING_SYNC.PRIMARY_SOURCES.BING.TOOLTIP.TEXT' | translate }}
      <div>
        <ul>
          <li>
            {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.BUSINESS_NAME' | translate }}
          </li>
          <li>
            {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.ADDRESS' | translate }}
          </li>
          <li>
            {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.WEBSITE' | translate }}
          </li>
          <li>
            <span [innerHTML]="'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.LOGO' | translate"></span>
          </li>
          <li>
            {{ 'LISTING_SYNC.PRIMARY_SOURCES.BING.ATTRIBUTES' | translate }}
          </li>
          <li>
            {{ 'LISTING_SYNC.LS_FREE_ADDON.COMMON_TOOLTIP.PRIMARY_PHONE' | translate }}
          </li>
          <li>
            {{ 'LISTING_SYNC.PRIMARY_SOURCES.BING.HOURS' | translate }}
          </li>
        </ul>
      </div>
    </mat-dialog-content>
  </mat-card>
</ng-template>

<mat-dialog-actions align="end">
  <button mat-stroked-button class="data" (click)="onCancel()">Close</button>
</mat-dialog-actions>
